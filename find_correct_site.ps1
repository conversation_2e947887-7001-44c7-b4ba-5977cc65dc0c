# Script to find the correct SharePoint site URL
Write-Host "=== Finding Correct SharePoint Site ===" -ForegroundColor Green

# Common SharePoint site patterns for your tenant
$possibleSites = @(
    "https://pindikv.sharepoint.com",
    "https://pindikv.sharepoint.com/sites/pkvh", 
    "https://pindikv.sharepoint.com/sites/default",
    "https://pindikv.sharepoint.com/sites/team",
    "https://pindikv.sharepoint.com/sites/intranet",
    "https://pindikv.sharepoint.com/sites/documents"
)

$workingSite = $null

foreach ($siteUrl in $possibleSites) {
    Write-Host "`nTrying: $siteUrl" -ForegroundColor Yellow
    
    try {
        # Try to connect
        Connect-PnPOnline -Url $siteUrl -DeviceLogin -ClientId "1950a258-227b-4e31-a9cf-717495945fc2"
        
        # Test if we can actually access the site
        $web = Get-PnPWeb -ErrorAction Stop
        
        Write-Host "✓ SUCCESS! Connected to: $($web.Title)" -ForegroundColor Green
        Write-Host "  Site URL: $($web.Url)"
        
        # Try to list libraries
        try {
            $libraries = Get-PnPList | Where-Object {$_.BaseTemplate -eq 101}
            Write-Host "  Found $($libraries.Count) document libraries:" -ForegroundColor Cyan
            foreach ($lib in $libraries) {
                Write-Host "    - $($lib.Title)"
                
                # Check if this is the 'pkvh' library we're looking for
                if ($lib.Title -eq "pkvh" -or $lib.EntityTypeName -eq "pkvh") {
                    Write-Host "      *** This might be the library we need! ***" -ForegroundColor Magenta
                }
            }
        } catch {
            Write-Host "  Could not list libraries: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        $workingSite = $siteUrl
        break
        
    } catch {
        Write-Host "✗ Failed: $($_.Exception.Message)" -ForegroundColor Red
        continue
    }
}

if ($workingSite) {
    Write-Host "`n=== RESULT ===" -ForegroundColor Green
    Write-Host "Working site URL: $workingSite" -ForegroundColor Green
    Write-Host "You can use this URL in your main script." -ForegroundColor Green
} else {
    Write-Host "`n=== NO WORKING SITE FOUND ===" -ForegroundColor Red
    Write-Host "Possible reasons:" -ForegroundColor Yellow
    Write-Host "1. You don't have access to any SharePoint sites in this tenant"
    Write-Host "2. The site might be at a different URL pattern"
    Write-Host "3. You might need to be granted explicit permissions"
    Write-Host "4. The site might be in a different tenant"
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Contact your SharePoint administrator"
    Write-Host "2. Ask them to provide the exact site URL"
    Write-Host "3. Ask them to grant you access to the site and the 'pkvh' library"
}
