import os
import qrcode
from PIL import Image, ImageDraw, ImageFont

# Settings
input_folder = "shared_folder"
output_folder = "qr_output"
os.makedirs(output_folder, exist_ok=True)
logo_path = os.path.join(input_folder, "logo.jpeg")
font_path = "/System/Library/Fonts/Arial.ttf"  # macOS system font path

# Load and resize logo once
original_logo = Image.open(logo_path)

# Iterate over each .txt file in the folder
for filename in os.listdir(input_folder):
    if filename.endswith(".txt"):
        client_name = os.path.splitext(filename)[0]
        with open(os.path.join(input_folder, filename), 'r') as f:
            url = f.read().strip()

        # Generate QR Code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_H,
            box_size=10,
            border=4,
        )
        qr.add_data(url)
        qr.make(fit=True)
        qr_img = qr.make_image(fill_color="green", back_color="white").convert("RGB")

        # Resize logo larger than before
        qr_width, qr_height = qr_img.size
        logo_width = qr_width // 2  # Bigger
        logo_ratio = logo_width / original_logo.width
        logo_height = int(original_logo.height * logo_ratio)
        logo = original_logo.resize((logo_width, logo_height))

        # Custom text with much larger font
        text = f"Pindi Kinnisvarahaldus\n{client_name}"
        font_size = 36  # 2 times bigger text (was 48)

        # Try multiple font paths to ensure we get a scalable font
        font = None
        font_paths = [
            "/System/Library/Fonts/Arial.ttf",
            "/System/Library/Fonts/Helvetica.ttc",
            "/Library/Fonts/Arial.ttf",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux fallback
        ]

        for path in font_paths:
            try:
                font = ImageFont.truetype(path, font_size)
                print(f"Using font: {path} at size {font_size}")
                break
            except (IOError, OSError):
                continue

        # If no TrueType font found, create a larger default font
        if font is None:
            try:
                # Try to load default font and scale it
                font = ImageFont.load_default()
                print(f"Using default font at size {font_size}")
            except:
                font = ImageFont.load_default()

        # Calculate text size
        temp_img = Image.new("RGB", (qr_width, 400), "white")  # Increased height for larger text
        temp_draw = ImageDraw.Draw(temp_img)
        text_lines = text.split("\n")
        text_heights = []
        text_width = 0
        for line in text_lines:
            bbox = temp_draw.textbbox((0, 0), line, font=font)
            w, h = bbox[2] - bbox[0], bbox[3] - bbox[1]
            text_width = max(text_width, w)
            text_heights.append(h)
        total_text_height = sum(text_heights) + (len(text_lines)-1)*20  # More spacing for larger text

        # Final combined image
        padding = 30
        total_height = qr_height + total_text_height + logo_height + 3 * padding
        final_img = Image.new("RGB", (qr_width, total_height), "white")
        final_img.paste(qr_img, (0, 0))

        # Draw text
        draw = ImageDraw.Draw(final_img)
        current_y = qr_height + padding
        for line, h in zip(text_lines, text_heights):
            line_width = draw.textlength(line, font=font)
            draw.text(((qr_width - line_width) // 2, current_y), line, fill="green", font=font)
            current_y += h + 20  # More spacing for larger text

        # Paste logo
        logo_x = (qr_width - logo_width) // 2
        final_img.paste(logo, (logo_x, current_y))

        # Save result
        output_path = os.path.join(output_folder, f"{client_name}_qr.png")
        final_img.save(output_path)
        print(f"Saved: {output_path}")