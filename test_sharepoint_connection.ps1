# Test SharePoint Connection and List Sites/Libraries
# This script will test the connection and explore what's available

Write-Host "=== SharePoint Connection Test ===" -ForegroundColor Green

# Try to connect using the same method that worked before
try {
    Write-Host "Attempting to connect to SharePoint..." -ForegroundColor Yellow
    Connect-PnPOnline -Url "https://pindikv.sharepoint.com" -DeviceLogin -ClientId "1950a258-227b-4e31-a9cf-717495945fc2"
    Write-Host "✓ Successfully connected to SharePoint" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to connect: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 1: Get current web information
Write-Host "`n=== Current Site Information ===" -ForegroundColor Green
try {
    $web = Get-PnPWeb
    Write-Host "Site Title: $($web.Title)"
    Write-Host "Site URL: $($web.Url)"
    Write-Host "Site Description: $($web.Description)"
} catch {
    Write-Host "✗ Could not get site information: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: List all document libraries
Write-Host "`n=== Document Libraries ===" -ForegroundColor Green
try {
    $libraries = Get-PnPList | Where-Object {$_.BaseTemplate -eq 101}
    if ($libraries) {
        Write-Host "Found $($libraries.Count) document libraries:" -ForegroundColor Yellow
        foreach ($lib in $libraries) {
            Write-Host "  - $($lib.Title) (Internal Name: $($lib.EntityTypeName))"
        }
    } else {
        Write-Host "No document libraries found or no access" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ Could not list libraries: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Try to access the specific library mentioned in the original script
Write-Host "`n=== Testing Access to 'pkvh' Library ===" -ForegroundColor Green
try {
    $pkvhLibrary = Get-PnPList -Identity "pkvh"
    Write-Host "✓ Successfully found 'pkvh' library: $($pkvhLibrary.Title)"
    
    # Try to list root folders in this library
    Write-Host "Root folders in 'pkvh' library:"
    $rootItems = Get-PnPFolderItem -FolderSiteRelativeUrl "pkvh" -ItemType Folder
    foreach ($folder in $rootItems) {
        Write-Host "  - $($folder.Name)"
    }
} catch {
    Write-Host "✗ Could not access 'pkvh' library: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: List all lists (not just document libraries)
Write-Host "`n=== All Lists ===" -ForegroundColor Green
try {
    $allLists = Get-PnPList
    Write-Host "Found $($allLists.Count) total lists:" -ForegroundColor Yellow
    foreach ($list in $allLists) {
        Write-Host "  - $($list.Title) (Type: $($list.BaseTemplate), Template: $($list.ListExperienceOptions))"
    }
} catch {
    Write-Host "✗ Could not list all lists: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Check current user permissions
Write-Host "`n=== Current User Information ===" -ForegroundColor Green
try {
    $currentUser = Get-PnPCurrentUser
    Write-Host "Current User: $($currentUser.Title)"
    Write-Host "Email: $($currentUser.Email)"
    Write-Host "Login Name: $($currentUser.LoginName)"
} catch {
    Write-Host "✗ Could not get current user info: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
Write-Host "If you see errors above, it indicates permission issues or incorrect site/library names."
