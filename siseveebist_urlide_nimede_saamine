# Config - Try different possible site URLs
$possibleSiteUrls = @(
    "https://pindikv.sharepoint.com",
    "https://pindikv.sharepoint.com/sites/pkvh",
    "https://pindikv.sharepoint.com/sites/default"
)
$libraryName = "pkvh"
$parentFolderPath = "ATS-SE Aktid"  # Change to the main folder name
$outputFile = "/Users/<USER>/Documents/augment-projects/bulk qr/sharepoint_links.txt"

# Try to find the correct site URL
$connectedSiteUrl = $null
foreach ($testUrl in $possibleSiteUrls) {
    Write-Host "Trying to connect to: $testUrl"
    try {
        Connect-PnPOnline -Url $testUrl -DeviceLogin -ClientId "1950a258-227b-4e31-a9cf-717495945fc2"

        # Test if we can access the site
        $web = Get-PnPWeb
        Write-Host "Successfully connected to: $($web.Title) at $($web.Url)"
        $connectedSiteUrl = $testUrl
        break
    } catch {
        Write-Host "Failed to connect to $testUrl : $($_.Exception.Message)"
        continue
    }
}

if (-not $connectedSiteUrl) {
    Write-Host "Could not connect to any of the attempted site URLs."
    Write-Host "Please check:"
    Write-Host "1. That you have access to the SharePoint site"
    Write-Host "2. The correct site URL (it might be a subsite)"
    Write-Host "3. That the site exists and you have permissions"
    exit 1
}

# Connection is handled above

# Debug: Check if we can access the library first
Write-Host "Checking access to library: $libraryName"
try {
    $library = Get-PnPList -Identity $libraryName
    Write-Host "Successfully found library: $($library.Title)"
} catch {
    Write-Host "Error accessing library '$libraryName': $($_.Exception.Message)"
    Write-Host "Available libraries:"
    try {
        Get-PnPList | Where-Object {$_.BaseTemplate -eq 101} | Select-Object Title | ForEach-Object { Write-Host "  - $($_.Title)" }
    } catch {
        Write-Host "Could not list libraries"
    }
    exit 1
}

# Debug: Check if the parent folder exists
Write-Host "Checking access to folder: $libraryName/$parentFolderPath"
try {
    $parentFolder = Get-PnPFolder -Url "$libraryName/$parentFolderPath"
    Write-Host "Successfully found parent folder: $($parentFolder.Name)"
} catch {
    Write-Host "Error accessing folder '$libraryName/$parentFolderPath': $($_.Exception.Message)"
    Write-Host "Trying to list root folders in library..."
    try {
        $rootFolders = Get-PnPFolderItem -FolderSiteRelativeUrl $libraryName -ItemType Folder
        Write-Host "Available folders in library root:"
        $rootFolders | ForEach-Object { Write-Host "  - $($_.Name)" }
    } catch {
        Write-Host "Could not list root folders"
    }
    exit 1
}

# Get all subfolders inside the parent folder
Write-Host "Getting subfolders from: $libraryName/$parentFolderPath"
try {
    $folders = Get-PnPFolderItem -FolderSiteRelativeUrl "$libraryName/$parentFolderPath" -ItemType Folder
    Write-Host "Found $($folders.Count) subfolders"
} catch {
    Write-Host "Error getting subfolders: $($_.Exception.Message)"
    exit 1
}

# Prepare output
$output = @()

foreach ($folder in $folders) {
    $folderPath = "$libraryName/$parentFolderPath/$($folder.Name)"

    # Create or get anonymous sharing link
    $sharingLink = Grant-PnPFolderAccess -Identity $folderPath -LinkType View -Scope Anonymous | Select-Object -ExpandProperty Link

    # Format line
    $line = "$($sharingLink.WebUrl) - $($folder.Name)"
    $output += $line
}

# Write to file
$output | Out-File -FilePath $outputFile -Encoding UTF8

Write-Host "Done. Links saved to $outputFile"